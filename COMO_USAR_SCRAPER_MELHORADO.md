# Como Usar o Scraper Melhorado

Este documento explica como usar as melhorias implementadas no scraper do Google Maps, baseadas nas estratégias documentadas em `MELHORIAS_SCRAPER.md`.

## 🚀 Principais Melhorias Implementadas

### 1. **Extração Baseada em place_id**
- ✅ Evita duplicatas usando identificadores únicos do Google
- ✅ Mantém controle preciso dos leads já processados
- ✅ Funciona mesmo quando a lista é recarregada

### 2. **Estratégia de Tiles Geográficos**
- ✅ Supera o limite de ~120 resultados do Google Maps
- ✅ Divide a área em uma grade de coordenadas (ex: 5x5 = 25 tiles)
- ✅ Busca independente em cada tile para maximizar resultados

### 3. **Extração Direta da Lista Lateral**
- ✅ Extrai dados básicos sem clicar em cada elemento
- ✅ Reduz problemas de DOM instável
- ✅ Muito mais rápido que a abordagem tradicional

### 4. **Integração com Google Places API**
- ✅ Enriquece dados básicos com informações detalhadas
- ✅ Obtém telefone, endereço completo, site, avaliações
- ✅ Funciona de forma opcional (sem API key mantém dados básicos)

### 5. **Melhor Tratamento de Erros**
- ✅ Recuperação automática de falhas
- ✅ Logs detalhados para diagnóstico
- ✅ Continuidade mesmo com tiles problemáticos

## 📋 Como Usar

### Opção 1: Usar a Classe ScraperMelhorado (Recomendado)

```python
from scraper_melhorado import ScraperMelhorado

# Configurações
CEP = "01310-100"  # Paulista, SP
PALAVRA_CHAVE = "dentista"
QUANTIDADE_DESEJADA = 100
API_KEY = "sua_chave_google_places_api"  # Opcional

# Usar context manager (recomendado)
with ScraperMelhorado(api_key=API_KEY, headless=False) as scraper:
    leads = scraper.extrair_leads(
        cep=CEP,
        palavra_chave=PALAVRA_CHAVE,
        quantidade_desejada=QUANTIDADE_DESEJADA,
        usar_tiles=True,  # Usar estratégia de tiles
        grid_size=5       # Grade 5x5 = 25 tiles
    )
    
    if leads:
        arquivo = scraper.salvar_resultados(leads)
        print(f"✅ {len(leads)} leads salvos em: {arquivo}")
```

### Opção 2: Usar Funções Diretamente

```python
from selenium import webdriver
from logic_bot import (
    buscar_cep, 
    buscar_palavra_chave, 
    extrair_clientes_com_detalhes_completos
)

# Configurar driver
driver = webdriver.Chrome()
driver.get('https://www.google.com/maps/')

# Buscar localização e termo
buscar_cep(driver, "01310-100")
buscar_palavra_chave(driver, "restaurante")

# Extrair com estratégias melhoradas
def meu_callback(atual, total, cliente):
    print(f"Progresso: {atual}/{total} - {cliente['nome']}")

leads = extrair_clientes_com_detalhes_completos(
    driver=driver,
    quantidade_desejada=50,
    progress_callback=meu_callback,
    usar_tiles=True,
    api_key="sua_api_key",  # Opcional
    grid_size=3  # Grade 3x3 = 9 tiles
)

driver.quit()
```

## ⚙️ Configurações Importantes

### Estratégia de Tiles
- **`usar_tiles=True`**: Usa tiles geográficos (recomendado para >50 leads)
- **`usar_tiles=False`**: Usa rolagem tradicional melhorada
- **`grid_size`**: Tamanho da grade (3=9 tiles, 5=25 tiles, 7=49 tiles)

### Google Places API
- **Com API Key**: Dados completos (telefone, site, avaliações, horários)
- **Sem API Key**: Dados básicos (nome, endereço básico, avaliação da lista)

### Modo Headless
- **`headless=False`**: Mostra o navegador (recomendado para desenvolvimento)
- **`headless=True`**: Execução em background (para produção)

## 📊 Comparação de Performance

| Estratégia | Leads Típicos | Tempo Médio | Dados Obtidos |
|------------|---------------|-------------|---------------|
| **Tradicional** | ~50-120 | 10-20 min | Nome, telefone básico |
| **Tiles (3x3)** | ~200-400 | 15-25 min | Nome, endereço, avaliação |
| **Tiles + API** | ~200-400 | 20-30 min | Dados completos |

## 🔧 Solução de Problemas

### Problema: "Nenhum elemento encontrado"
**Solução**: Verificar se o CEP e palavra-chave são válidos
```python
# Teste manual
with ScraperMelhorado() as scraper:
    # Verificar se encontra resultados básicos primeiro
    leads = scraper.extrair_leads("01310-100", "loja", 5, usar_tiles=False)
```

### Problema: "Poucos resultados encontrados"
**Solução**: Aumentar grid_size ou usar termos mais genéricos
```python
# Aumentar cobertura geográfica
leads = scraper.extrair_leads(
    cep="01310-100",
    palavra_chave="loja",  # Termo mais genérico
    quantidade_desejada=100,
    grid_size=7  # Grade maior = mais cobertura
)
```

### Problema: "Erro na Places API"
**Solução**: Verificar cota e chave da API
```python
# Funciona sem API key (dados básicos)
with ScraperMelhorado(api_key=None) as scraper:
    leads = scraper.extrair_leads(cep, palavra_chave, quantidade)
```

## 📝 Logs e Monitoramento

O scraper melhorado gera logs detalhados:
- **`scraper_melhorado_YYYYMMDD_HHMMSS.log`**: Log específico da execução
- **`g_finder.log`**: Log geral do sistema

Exemplo de log:
```
2024-01-15 10:30:15 - G-Finder - INFO - Processando tile 1/25: (-23.550520, -46.633308)
2024-01-15 10:30:18 - G-Finder - INFO - Tile 1: 8 novos clientes encontrados
2024-01-15 10:30:20 - G-Finder - INFO - Cliente 1 enriquecido: Restaurante ABC
```

## 🎯 Dicas de Otimização

### Para Máxima Quantidade de Leads:
```python
leads = scraper.extrair_leads(
    cep="01310-100",
    palavra_chave="loja",  # Termo genérico
    quantidade_desejada=500,
    usar_tiles=True,
    grid_size=7  # Grade 7x7 = 49 tiles
)
```

### Para Máxima Qualidade de Dados:
```python
leads = scraper.extrair_leads(
    cep="01310-100",
    palavra_chave="dentista especialista",  # Termo específico
    quantidade_desejada=50,
    usar_tiles=True,
    grid_size=3  # Cobertura menor, mais focada
)
# + usar API key para dados completos
```

### Para Máxima Velocidade:
```python
leads = scraper.extrair_leads(
    cep="01310-100",
    palavra_chave="restaurante",
    quantidade_desejada=30,
    usar_tiles=False  # Rolagem tradicional é mais rápida para poucos leads
)
```

## 🔑 Obtendo Google Places API Key

1. Acesse [Google Cloud Console](https://console.cloud.google.com/)
2. Crie um projeto ou selecione existente
3. Ative a "Places API"
4. Crie credenciais (API Key)
5. Configure restrições de uso (opcional)

**Cota gratuita**: 1.000 consultas/mês
**Preço**: ~$17 por 1.000 consultas adicionais

## 📞 Suporte

Para problemas ou dúvidas:
1. Verificar logs gerados
2. Testar com configurações mais simples
3. Verificar conectividade e CEP válido
4. Consultar documentação do Google Maps/Places API
