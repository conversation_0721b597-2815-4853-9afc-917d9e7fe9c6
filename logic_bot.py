from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium import webdriver
import pandas as pd
import time
import logging
import os
import datetime
import re
import requests
import json

# Configuração de log
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('g_finder.log')
    ]
)

logger = logging.getLogger('G-Finder')

def buscar_cep(driver, cep):
    """
    Busca um CEP no Google Maps

    Args:
        driver: WebDriver do Selenium
        cep: String contendo o CEP a ser buscado
    """
    try:
        logger.info(f"Buscando pelo CEP: {cep}")

        # Limpa o texto prévio e espera pelo campo de busca
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )
        search_box.clear()

        # Formata o CEP e insere no campo de busca
        cep_formatado = re.sub(r'[^0-9]', '', cep)  # Remove caracteres não numéricos
        if len(cep_formatado) == 8:
            cep_formatado = f"{cep_formatado[:5]}-{cep_formatado[5:]}"

        search_box.send_keys(cep_formatado)
        search_box.send_keys(Keys.ENTER)

        # Espera para carregar os resultados
        logger.info("Aguardando resultados do CEP...")
        time.sleep(5)

        # Verifica se o CEP foi encontrado
        try:
            WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.XPATH, '//h1[contains(@class, "fontHeadlineLarge")]'))
            )
            logger.info(f"CEP {cep} encontrado com sucesso")
            return True
        except:
            logger.warning(f"Não foi possível confirmar se o CEP {cep} foi encontrado corretamente")
            return True  # Assume que o CEP foi encontrado mesmo sem confirmação

    except Exception as e:
        logger.error(f'Falha ao buscar CEP {cep}: {str(e)}')
        return False

def buscar_palavra_chave(driver, palavra_chave):
    """
    Busca uma palavra-chave no Google Maps na região atual

    Args:
        driver: WebDriver do Selenium
        palavra_chave: String contendo o termo a ser buscado
    """
    try:
        logger.info(f"Buscando pela palavra-chave: {palavra_chave}")

        # Espera pela presença do campo de busca
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )

        # Limpa o texto anterior
        search_box.send_keys(Keys.CONTROL + "a")
        search_box.send_keys(Keys.DELETE)

        # Insere a palavra-chave
        search_box.send_keys(palavra_chave)
        search_box.send_keys(Keys.ENTER)

        # Aguarda pelo carregamento dos resultados
        logger.info("Aguardando resultados da busca...")
        time.sleep(5)

        # Verifica se há resultados
        try:
            resultados = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
            )
            logger.info(f"Encontrados {len(resultados)} resultados iniciais para '{palavra_chave}'")
            return True
        except:
            logger.warning(f"Não foram encontrados resultados para '{palavra_chave}'")
            return False

    except Exception as e:
        logger.error(f'Falha ao buscar palavra-chave {palavra_chave}: {str(e)}')
        return False

def extrair_clientes(driver, quantidade_desejada, progress_callback):
    """
    Extrai informações de clientes com base nos resultados da busca

    Args:
        driver: WebDriver do Selenium
        quantidade_desejada: Número de clientes a serem extraídos
        progress_callback: Função de callback para atualizar o progresso

    Returns:
        Lista de dicionários contendo informações dos clientes
    """
    clientes_extraidos = []
    clientes_unicos = set()
    contador = 0
    tentativas_sem_novos = 0
    MAX_TENTATIVAS_SEM_NOVOS = 10

    # Obter a palavra-chave atual da busca para uso em caso de recuperação
    try:
        palavra_chave_atual = driver.find_element(By.XPATH, '//*[@id="searchboxinput"]').get_attribute('value')
    except:
        palavra_chave_atual = "Loja"  # Valor padrão caso não consiga obter

    logger.info(f"Iniciando extração de {quantidade_desejada} leads...")

    try:
        # Contador para tentativas consecutivas de encontrar elementos
        tentativas_encontrar_elementos = 0
        MAX_TENTATIVAS_ENCONTRAR_ELEMENTOS = 5

        # Loop principal para extrair clientes
        while contador < quantidade_desejada and tentativas_sem_novos < MAX_TENTATIVAS_SEM_NOVOS:
            # Coleta todos os elementos de resultados visíveis na página
            try:
                elementos = WebDriverWait(driver, 10).until(
                    EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
                )
                logger.info(f"Encontrados {len(elementos)} elementos na página atual")
                tentativas_encontrar_elementos = 0  # Resetar contador se encontrou elementos
            except:
                logger.warning("Não foi possível encontrar elementos na página atual")
                tentativas_encontrar_elementos += 1

                # Se falhar muitas vezes consecutivas, tenta recarregar a página
                if tentativas_encontrar_elementos >= MAX_TENTATIVAS_ENCONTRAR_ELEMENTOS:
                    logger.warning(f"Falhou {MAX_TENTATIVAS_ENCONTRAR_ELEMENTOS} vezes consecutivas ao tentar encontrar elementos. Tentando recarregar a página.")
                    try:
                        # Recarregar a página e refazer a busca
                        driver.get('https://www.google.com/maps/')
                        time.sleep(3)
                        search_box = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                        )
                        search_box.send_keys(palavra_chave_atual)
                        search_box.send_keys(Keys.ENTER)
                        time.sleep(5)
                        logger.info("Página recarregada após falhas consecutivas")
                        tentativas_encontrar_elementos = 0  # Resetar contador
                    except Exception as reload_error:
                        logger.error(f"Erro ao recarregar a página: {str(reload_error)}")
                        # Se não conseguir recarregar, interrompe a extração
                        break

                scroll_down(driver)
                time.sleep(2)
                continue

            # Se já processamos todos os elementos visíveis, role para baixo para carregar mais
            if contador >= len(elementos):
                logger.info("Rolando para baixo para carregar mais resultados...")
                scroll_down(driver)
                time.sleep(2)
                tentativas_sem_novos += 1
                continue

            # Tenta clicar no elemento para abrir os detalhes
            try:
                logger.info(f"Clicando no resultado {contador + 1}")
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", elementos[contador])
                time.sleep(0.5)
                elementos[contador].click()
                time.sleep(2)
                tentativas_sem_novos = 0  # Resetar contador de tentativas se conseguiu clicar
            except Exception as e:
                logger.warning(f"Não foi possível clicar no resultado {contador + 1}: {str(e)}")
                contador += 1
                continue

            # Extrai as informações do cliente
            try:
                # Extrair o nome
                nome_elemento = WebDriverWait(driver, 7).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1'))
                )
                nome_cliente = nome_elemento.text.strip()

                # Pular se o cliente já foi processado
                if nome_cliente in clientes_unicos:
                    logger.info(f"Cliente '{nome_cliente}' já foi processado, pulando...")
                    contador += 1
                    continue

                # Extrair informações adicionais
                cliente = {
                    'nome': nome_cliente,
                    'telefone': "Telefone não disponível",
                    'endereco': "Endereço não disponível",
                    'site': "Site não disponível"
                }

                # Função auxiliar para limpar e formatar números de telefone
                def limpar_telefone(texto):
                    # Remover textos específicos que não são telefones
                    if not texto or texto.strip() == "":
                        return "Telefone não disponível"

                    texto = re.sub(r'Enviar para o smartphone', '', texto, flags=re.IGNORECASE)

                    # Extrair apenas dígitos, parênteses, traços e espaços
                    texto_limpo = re.sub(r'[^\d\(\)\s\-\+]', '', texto)

                    # Aplicar regex para extrair apenas o número de telefone
                    telefone_match = re.search(r'(?:\+?55\s?)?(?:\(?\d{2}\)?[\s.-]?)?\d{4,5}[\s.-]?\d{4}', texto_limpo)

                    if telefone_match:
                        # Apenas extrair o número encontrado
                        return telefone_match.group(0)

                    # Se não encontrar um padrão válido de telefone, verificar se pelo menos tem dígitos
                    digitos = re.sub(r'\D', '', texto_limpo)
                    if len(digitos) >= 8:  # Telefone tem pelo menos 8 dígitos
                        # Formatar o número encontrado
                        if len(digitos) >= 10:  # Com DDD
                            return f"({digitos[:2]}) {digitos[2:6]}-{digitos[6:]}"
                        else:  # Sem DDD
                            return f"{digitos[:4]}-{digitos[4:]}"

                    return "Telefone não disponível"

                # Extrair telefone
                try:
                    # Primeira tentativa - capturar do botão com data-item-id específico para telefone
                    telefone_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//button[@data-item-id[contains(., "phone:tel:")]]'))
                    )
                    # Extrair do aria-label que contém o número formatado
                    aria_label = telefone_elemento.get_attribute('aria-label')
                    if aria_label and "telefone:" in aria_label.lower():
                        # Extrair o número do aria-label
                        telefone_texto = aria_label.split(':', 1)[1].strip()
                        cliente['telefone'] = limpar_telefone(telefone_texto)
                    else:
                        # Capturar do texto do elemento interno
                        try:
                            telefone_interno = telefone_elemento.find_element(By.XPATH, './/div[contains(@class, "Io6YTe") or contains(@class, "fontBodyMedium")]')
                            telefone_texto = telefone_interno.text.strip()
                            cliente['telefone'] = limpar_telefone(telefone_texto)
                        except:
                            # Se não encontrar o elemento interno, usar o texto do botão
                            telefone_texto = telefone_elemento.text.strip()
                            cliente['telefone'] = limpar_telefone(telefone_texto)
                except:
                    try:
                        # Segunda tentativa - capturar do botão com classe CsEnBe que tem aria-label contendo "Telefone:"
                        telefone_elemento = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, '//button[contains(@class, "CsEnBe") and contains(@aria-label, "Telefone:")]'))
                        )
                        aria_label = telefone_elemento.get_attribute('aria-label')
                        if aria_label:
                            # Extrair o número do aria-label
                            telefone_texto = aria_label.split(':', 1)[1].strip()
                            cliente['telefone'] = limpar_telefone(telefone_texto)
                        else:
                            # Tentar capturar da div interna
                            try:
                                telefone_interno = telefone_elemento.find_element(By.XPATH, './/div[contains(@class, "rogA2c")]//div[contains(@class, "fontBodyMedium")]')
                                telefone_texto = telefone_interno.text.strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                            except:
                                telefone_texto = telefone_elemento.text.strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                    except:
                        try:
                            # Terceira tentativa - capturar de links href="tel:"
                            telefone_elemento = WebDriverWait(driver, 3).until(
                                EC.presence_of_element_located((By.XPATH, '//a[contains(@href, "tel:")]'))
                            )
                            href = telefone_elemento.get_attribute('href')
                            if href and 'tel:' in href:
                                telefone_texto = href.split('tel:')[1].strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                            else:
                                telefone_texto = telefone_elemento.text.strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                        except:
                            logger.warning(f"Telefone não disponível para '{nome_cliente}'")
                            cliente['telefone'] = "Telefone não disponível"

                # Extrair endereço (opcional)
                try:
                    endereco_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//button[contains(@data-item-id, "address")]'))
                    )
                    cliente['endereco'] = endereco_elemento.text.strip()
                except:
                    logger.warning(f"Endereço não disponível para '{nome_cliente}'")

                # Extrair site (opcional)
                try:
                    site_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//a[contains(@data-item-id, "authority")]'))
                    )
                    cliente['site'] = site_elemento.get_attribute('href')
                except:
                    logger.warning(f"Site não disponível para '{nome_cliente}'")

                # Adicionar cliente à lista
                clientes_extraidos.append(cliente)
                clientes_unicos.add(nome_cliente)

                logger.info(f"Lead {contador + 1}: {nome_cliente} - {cliente['telefone']}")

                # Atualizar progresso
                progress_callback(contador + 1, quantidade_desejada, cliente)

                contador += 1

            except Exception as e:
                logger.error(f"Erro ao extrair dados do cliente {contador + 1}: {str(e)}")
                contador += 1
                continue

            # Voltar para a lista de resultados
            tentativas_voltar = 0
            max_tentativas_voltar = 3
            sucesso_voltar = False

            while tentativas_voltar < max_tentativas_voltar and not sucesso_voltar:
                try:
                    # Método 1: Botão voltar
                    voltar_button = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, '//button[@aria-label="Voltar"]'))
                    )
                    voltar_button.click()
                    time.sleep(1.5)
                    sucesso_voltar = True
                    logger.info("Voltou para a lista de resultados usando o botão Voltar")
                except:
                    try:
                        # Método 2: JavaScript history.back()
                        logger.warning("Não foi possível voltar usando o botão, tentando history.back()")
                        driver.execute_script("history.back()")
                        time.sleep(2)

                        # Verificar se voltou para a lista de resultados
                        elementos_apos_voltar = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
                        if len(elementos_apos_voltar) > 0:
                            sucesso_voltar = True
                            logger.info("Voltou para a lista de resultados usando history.back()")
                        else:
                            # Método 3: Tentar clicar em qualquer área fora do card de detalhes
                            try:
                                logger.warning("Não foi possível voltar com history.back(), tentando clicar fora do card")
                                driver.find_element(By.XPATH, '//div[@class="aomaec"]').click()
                                time.sleep(1)
                                sucesso_voltar = True
                            except:
                                # Método 4: Pressionar ESC
                                try:
                                    logger.warning("Tentando pressionar ESC para fechar o card")
                                    webdriver.ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                                    time.sleep(1)
                                    sucesso_voltar = True
                                except:
                                    # Método 5: Recarregar a página e refazer a busca
                                    if tentativas_voltar == max_tentativas_voltar - 1:  # Última tentativa
                                        logger.warning("Tentando recarregar a página e refazer a busca")
                                        driver.refresh()
                                        time.sleep(3)

                                        # Tentar refazer a busca
                                        try:
                                            search_box = WebDriverWait(driver, 10).until(
                                                EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                                            )
                                            search_box.clear()
                                            search_box.send_keys(Keys.CONTROL + "a")
                                            search_box.send_keys(Keys.DELETE)
                                            search_box.send_keys(driver.current_url.split('@')[1].split(',')[0])  # Usar coordenadas da URL
                                            search_box.send_keys(Keys.ENTER)
                                            time.sleep(3)
                                            sucesso_voltar = True
                                        except:
                                            logger.error("Falha ao recarregar e refazer a busca")
                    except:
                        logger.error("Falha ao navegar de volta para a lista de resultados")

                tentativas_voltar += 1

            # Se não conseguiu voltar após todas as tentativas, tenta continuar mesmo assim
            if not sucesso_voltar:
                logger.error("Não foi possível voltar para a lista de resultados após várias tentativas")
                # Tentar recarregar a página como último recurso
                try:
                    driver.get('https://www.google.com/maps/')
                    time.sleep(3)
                    search_box = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                    )
                    search_box.send_keys(palavra_chave_atual)
                    search_box.send_keys(Keys.ENTER)
                    time.sleep(5)
                    logger.info("Página recarregada e busca refeita como último recurso")
                except:
                    logger.error("Falha ao recarregar a página como último recurso")

    except Exception as e:
        logger.error(f'Falha na extração: {str(e)}')

    finally:
        # Informações finais
        total_extraidos = len(clientes_extraidos)
        logger.info(f"Extração concluída. Total de leads extraídos: {total_extraidos}")

        if total_extraidos < quantidade_desejada:
            logger.warning(f"Atenção: foram solicitados {quantidade_desejada} leads, mas apenas {total_extraidos} foram encontrados.")

        return clientes_extraidos

# ============================================================================
# FUNÇÕES MELHORADAS BASEADAS NAS ESTRATÉGIAS DO MELHORIAS_SCRAPER.md
# ============================================================================

def extrair_place_id_do_href(href):
    """
    Extrai o place_id do href de um elemento do Google Maps

    Args:
        href: URL do elemento (ex: https://www.google.com/maps/place/?q=place_id:ChIJN1t_tDeuEmsRUsoyG83frY4)

    Returns:
        place_id ou None se não encontrado
    """
    if not href:
        return None

    if 'place_id:' in href:
        try:
            return href.split('place_id:')[1].split('&')[0]
        except:
            return None

    # Tentar extrair de outros formatos de URL do Google Maps
    if '/place/' in href and '@' in href:
        try:
            # Formato: /place/Nome+do+Local/@lat,lng,zoom
            coords_part = href.split('@')[1].split(',')
            if len(coords_part) >= 2:
                # Usar coordenadas como identificador único temporário
                lat, lng = coords_part[0], coords_part[1]
                return f"coords_{lat}_{lng}"
        except:
            return None

    return None

def obter_coordenadas_do_mapa(driver):
    """
    Obtém as coordenadas atuais do centro do mapa a partir da URL

    Args:
        driver: WebDriver do Selenium

    Returns:
        Tupla (latitude, longitude) ou None se não conseguir extrair
    """
    try:
        url_atual = driver.current_url
        # Formato: https://www.google.com/maps/@-23.550520,-46.633308,15z
        coords_match = re.search(r'@(-?\d+\.\d+),(-?\d+\.\d+)', url_atual)
        if coords_match:
            lat = float(coords_match.group(1))
            lng = float(coords_match.group(2))
            return (lat, lng)
    except Exception as e:
        logger.warning(f"Não foi possível extrair coordenadas da URL: {str(e)}")

    return None

def gerar_tiles_geograficos(lat_centro, lng_centro, desloc=0.01, grid_size=5):
    """
    Gera uma grade de tiles (coordenadas) para cobrir uma área geográfica

    Args:
        lat_centro: Latitude do centro
        lng_centro: Longitude do centro
        desloc: Deslocamento em graus para cada tile (~0.01 = ~1km)
        grid_size: Tamanho da grade (ex: 5 = 5x5 = 25 tiles)

    Returns:
        Lista de tuplas (latitude, longitude) para cada tile
    """
    tiles = []
    half_grid = grid_size // 2

    for i in range(-half_grid, half_grid + 1):
        for j in range(-half_grid, half_grid + 1):
            lat_tile = lat_centro + i * desloc
            lng_tile = lng_centro + j * desloc
            tiles.append((lat_tile, lng_tile))

    logger.info(f"Gerados {len(tiles)} tiles para cobertura geográfica")
    return tiles

def extrair_dados_lista_lateral(driver):
    """
    Extrai dados básicos diretamente da lista lateral sem clicar em cada elemento

    Args:
        driver: WebDriver do Selenium

    Returns:
        Lista de dicionários com dados básicos extraídos
    """
    dados_extraidos = []

    try:
        elementos = WebDriverWait(driver, 10).until(
            EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
        )

        logger.info(f"Extraindo dados básicos de {len(elementos)} elementos da lista lateral")

        for idx, elemento in enumerate(elementos):
            try:
                # Extrair href para obter place_id
                href = elemento.get_attribute('href')
                place_id = extrair_place_id_do_href(href)

                if not place_id:
                    continue

                # Extrair nome
                try:
                    nome_elem = elemento.find_element(By.XPATH, './/div[contains(@class,"fontHeadlineSmall")]')
                    nome = nome_elem.text.strip()
                except:
                    nome = f"Nome não disponível {idx}"

                # Extrair endereço básico (se disponível na lista)
                try:
                    endereco_elem = elemento.find_element(By.XPATH, './/span[contains(@class,"W4Efsd")]')
                    endereco = endereco_elem.text.strip()
                except:
                    endereco = "Endereço não disponível"

                # Extrair avaliação (se disponível)
                try:
                    avaliacao_elem = elemento.find_element(By.XPATH, './/span[contains(@class,"MW4etd")]')
                    avaliacao = avaliacao_elem.text.strip()
                except:
                    avaliacao = "Avaliação não disponível"

                dados_extraidos.append({
                    'place_id': place_id,
                    'nome': nome,
                    'endereco': endereco,
                    'avaliacao': avaliacao,
                    'telefone': "Telefone não disponível",
                    'site': "Site não disponível"
                })

            except Exception as e:
                logger.warning(f"Erro ao extrair dados do elemento {idx}: {str(e)}")
                continue

        logger.info(f"Extraídos dados básicos de {len(dados_extraidos)} elementos")
        return dados_extraidos

    except Exception as e:
        logger.error(f"Erro ao extrair dados da lista lateral: {str(e)}")
        return []

def scroll_down(driver):
    """
    Rola a página para baixo para carregar mais resultados

    Args:
        driver: WebDriver do Selenium
    """
    try:
        # Tenta encontrar o último elemento visível e rolar até ele
        elementos = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
        if elementos:
            driver.execute_script("arguments[0].scrollIntoView({block: 'end'});", elementos[-1])
            # Rola um pouco mais para garantir que novos elementos sejam carregados
            driver.execute_script("window.scrollBy(0, 200);")
        else:
            # Tenta encontrar o painel de resultados e rolar dentro dele
            try:
                painel_resultados = driver.find_element(By.XPATH, '//div[@role="feed"]')
                driver.execute_script("arguments[0].scrollTop = arguments[0].scrollTop + 500", painel_resultados)
            except:
                # Se não encontrar o painel, tenta rolar a página principal
                driver.execute_script("window.scrollBy(0, 500);")

            # Tenta clicar no botão "Ver mais resultados" se existir
            try:
                botao_mais = driver.find_element(By.XPATH, '//button[contains(., "Ver mais")]')
                botao_mais.click()
                logger.info("Clicou no botão 'Ver mais resultados'")
                time.sleep(1)
            except:
                pass
    except Exception as e:
        logger.error(f"Erro ao rolar a página: {str(e)}")
        # Fallback para rolagem simples
        driver.execute_script("window.scrollBy(0, 500);")

    # Aguarda um momento para o carregamento dos elementos
    time.sleep(1)

def extrair_clientes_melhorado(driver, quantidade_desejada, progress_callback, usar_tiles=True, grid_size=5):
    """
    Versão melhorada da extração que implementa as estratégias do MELHORIAS_SCRAPER.md

    Args:
        driver: WebDriver do Selenium
        quantidade_desejada: Número de clientes a serem extraídos
        progress_callback: Função de callback para atualizar o progresso
        usar_tiles: Se True, usa estratégia de tiles geográficos
        grid_size: Tamanho da grade de tiles (ex: 5 = 5x5 = 25 tiles)

    Returns:
        Lista de dicionários contendo informações dos clientes
    """
    clientes_extraidos = []
    clientes_unicos = set()  # Usar place_id para evitar duplicatas

    # Obter palavra-chave atual
    try:
        palavra_chave_atual = driver.find_element(By.XPATH, '//*[@id="searchboxinput"]').get_attribute('value')
    except:
        palavra_chave_atual = "Loja"

    logger.info(f"Iniciando extração melhorada de {quantidade_desejada} leads...")
    logger.info(f"Estratégia: {'Tiles geográficos' if usar_tiles else 'Rolagem tradicional'}")

    try:
        if usar_tiles:
            # Estratégia 1: Usar tiles geográficos
            coords = obter_coordenadas_do_mapa(driver)
            if not coords:
                logger.warning("Não foi possível obter coordenadas, usando estratégia tradicional")
                return extrair_clientes_tradicional(driver, quantidade_desejada, progress_callback)

            lat_centro, lng_centro = coords
            tiles = gerar_tiles_geograficos(lat_centro, lng_centro, grid_size=grid_size)

            for idx, (lat_tile, lng_tile) in enumerate(tiles):
                if len(clientes_extraidos) >= quantidade_desejada:
                    break

                logger.info(f"Processando tile {idx + 1}/{len(tiles)}: ({lat_tile:.6f}, {lng_tile:.6f})")

                # Navegar para o tile
                driver.get(f'https://www.google.com/maps/@{lat_tile},{lng_tile},16z')
                time.sleep(3)

                # Aguardar carregamento
                try:
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                    )
                except:
                    logger.warning(f"Timeout ao carregar tile {idx + 1}")
                    continue

                # Buscar palavra-chave no tile atual
                if not buscar_palavra_chave(driver, palavra_chave_atual):
                    logger.warning(f"Não foi possível buscar palavra-chave no tile {idx + 1}")
                    continue

                # Rolar para carregar mais resultados
                scroll_down(driver)
                time.sleep(2)

                # Extrair dados da lista lateral
                dados_tile = extrair_dados_lista_lateral(driver)

                # Filtrar duplicatas usando place_id
                novos_clientes = []
                for cliente in dados_tile:
                    if cliente['place_id'] not in clientes_unicos:
                        clientes_unicos.add(cliente['place_id'])
                        novos_clientes.append(cliente)
                        clientes_extraidos.append(cliente)

                        # Atualizar progresso
                        progress_callback(len(clientes_extraidos), quantidade_desejada, cliente)

                        if len(clientes_extraidos) >= quantidade_desejada:
                            break

                logger.info(f"Tile {idx + 1}: {len(novos_clientes)} novos clientes encontrados")

        else:
            # Estratégia 2: Rolagem tradicional melhorada
            clientes_extraidos = extrair_clientes_tradicional(driver, quantidade_desejada, progress_callback)

    except Exception as e:
        logger.error(f'Falha na extração melhorada: {str(e)}')

    finally:
        total_extraidos = len(clientes_extraidos)
        logger.info(f"Extração melhorada concluída. Total de leads extraídos: {total_extraidos}")

        if total_extraidos < quantidade_desejada:
            logger.warning(f"Atenção: foram solicitados {quantidade_desejada} leads, mas apenas {total_extraidos} foram encontrados.")

        return clientes_extraidos

def extrair_clientes_tradicional(driver, quantidade_desejada, progress_callback):
    """
    Versão melhorada da extração tradicional com place_id tracking

    Args:
        driver: WebDriver do Selenium
        quantidade_desejada: Número de clientes a serem extraídos
        progress_callback: Função de callback para atualizar o progresso

    Returns:
        Lista de dicionários contendo informações dos clientes
    """
    clientes_extraidos = []
    clientes_unicos = set()  # Usar place_id para evitar duplicatas
    tentativas_sem_novos = 0
    MAX_TENTATIVAS_SEM_NOVOS = 10

    logger.info("Usando estratégia tradicional melhorada com place_id tracking")

    try:
        while len(clientes_extraidos) < quantidade_desejada and tentativas_sem_novos < MAX_TENTATIVAS_SEM_NOVOS:
            # Rolar para carregar mais resultados
            scroll_down(driver)
            time.sleep(2)

            # Extrair dados da lista lateral
            dados_atuais = extrair_dados_lista_lateral(driver)

            # Contar novos clientes encontrados
            novos_encontrados = 0
            for cliente in dados_atuais:
                if cliente['place_id'] not in clientes_unicos:
                    clientes_unicos.add(cliente['place_id'])
                    clientes_extraidos.append(cliente)
                    novos_encontrados += 1

                    # Atualizar progresso
                    progress_callback(len(clientes_extraidos), quantidade_desejada, cliente)

                    if len(clientes_extraidos) >= quantidade_desejada:
                        break

            if novos_encontrados == 0:
                tentativas_sem_novos += 1
                logger.info(f"Nenhum novo cliente encontrado. Tentativa {tentativas_sem_novos}/{MAX_TENTATIVAS_SEM_NOVOS}")
            else:
                tentativas_sem_novos = 0
                logger.info(f"Encontrados {novos_encontrados} novos clientes")

    except Exception as e:
        logger.error(f'Falha na extração tradicional: {str(e)}')

    return clientes_extraidos

def obter_detalhes_place_api(place_id, api_key):
    """
    Obtém detalhes de um local usando a Google Places API

    Args:
        place_id: ID do local no Google Places
        api_key: Chave da API do Google Places

    Returns:
        Dicionário com detalhes do local ou None se houver erro
    """
    if not api_key or not place_id:
        return None

    try:
        url = f'https://maps.googleapis.com/maps/api/place/details/json'
        params = {
            'place_id': place_id,
            'key': api_key,
            'fields': 'name,formatted_address,formatted_phone_number,website,rating,user_ratings_total,opening_hours,price_level,types'
        }

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if data.get('status') == 'OK':
            result = data.get('result', {})
            return {
                'telefone': result.get('formatted_phone_number', 'Telefone não disponível'),
                'endereco': result.get('formatted_address', 'Endereço não disponível'),
                'site': result.get('website', 'Site não disponível'),
                'avaliacao': result.get('rating', 'Avaliação não disponível'),
                'total_avaliacoes': result.get('user_ratings_total', 'Total de avaliações não disponível'),
                'horario': str(result.get('opening_hours', {}).get('weekday_text', 'Horário não disponível')),
                'preco': result.get('price_level', 'Preço não disponível'),
                'categoria': ', '.join(result.get('types', ['Categoria não disponível']))
            }
        else:
            logger.warning(f"Erro na Places API para {place_id}: {data.get('status')}")
            return None

    except Exception as e:
        logger.error(f"Erro ao consultar Places API para {place_id}: {str(e)}")
        return None

def enriquecer_dados_com_api(clientes_extraidos, api_key=None, progress_callback=None):
    """
    Enriquece os dados básicos extraídos com informações detalhadas da Places API

    Args:
        clientes_extraidos: Lista de clientes com dados básicos
        api_key: Chave da API do Google Places (opcional)
        progress_callback: Função de callback para progresso (opcional)

    Returns:
        Lista de clientes com dados enriquecidos
    """
    if not api_key:
        logger.info("API key não fornecida, mantendo dados básicos")
        return clientes_extraidos

    logger.info(f"Enriquecendo dados de {len(clientes_extraidos)} clientes com Places API")
    clientes_enriquecidos = []

    for idx, cliente in enumerate(clientes_extraidos):
        try:
            place_id = cliente.get('place_id')
            if not place_id or place_id.startswith('coords_'):
                # Se não tem place_id válido, manter dados básicos
                clientes_enriquecidos.append(cliente)
                continue

            # Obter detalhes da API
            detalhes = obter_detalhes_place_api(place_id, api_key)

            if detalhes:
                # Atualizar dados do cliente com informações da API
                cliente_enriquecido = cliente.copy()
                cliente_enriquecido.update(detalhes)
                clientes_enriquecidos.append(cliente_enriquecido)
                logger.info(f"Cliente {idx + 1} enriquecido: {cliente_enriquecido['nome']}")
            else:
                # Se falhar na API, manter dados básicos
                clientes_enriquecidos.append(cliente)
                logger.warning(f"Falha ao enriquecer cliente {idx + 1}, mantendo dados básicos")

            # Atualizar progresso se callback fornecido
            if progress_callback:
                progress_callback(idx + 1, len(clientes_extraidos), cliente)

            # Pequena pausa para não sobrecarregar a API
            time.sleep(0.1)

        except Exception as e:
            logger.error(f"Erro ao enriquecer cliente {idx + 1}: {str(e)}")
            clientes_enriquecidos.append(cliente)

    logger.info(f"Enriquecimento concluído: {len(clientes_enriquecidos)} clientes processados")
    return clientes_enriquecidos

def extrair_clientes_com_detalhes_completos(driver, quantidade_desejada, progress_callback,
                                          usar_tiles=True, api_key=None, grid_size=5):
    """
    Função principal que combina todas as estratégias melhoradas

    Args:
        driver: WebDriver do Selenium
        quantidade_desejada: Número de clientes a serem extraídos
        progress_callback: Função de callback para atualizar o progresso
        usar_tiles: Se True, usa estratégia de tiles geográficos
        api_key: Chave da API do Google Places para dados detalhados (opcional)
        grid_size: Tamanho da grade de tiles

    Returns:
        Lista de dicionários contendo informações completas dos clientes
    """
    logger.info("=== INICIANDO EXTRAÇÃO COM ESTRATÉGIAS MELHORADAS ===")

    # Fase 1: Extração básica com estratégia otimizada
    clientes_basicos = extrair_clientes_melhorado(
        driver, quantidade_desejada, progress_callback, usar_tiles, grid_size
    )

    if not clientes_basicos:
        logger.warning("Nenhum cliente foi extraído na fase básica")
        return []

    # Fase 2: Enriquecimento com Places API (se disponível)
    if api_key:
        logger.info("=== INICIANDO ENRIQUECIMENTO COM PLACES API ===")
        clientes_completos = enriquecer_dados_com_api(clientes_basicos, api_key, progress_callback)
    else:
        logger.info("API key não fornecida, mantendo dados básicos")
        clientes_completos = clientes_basicos

    logger.info("=== EXTRAÇÃO COMPLETA FINALIZADA ===")
    return clientes_completos

def df_clientes(clientes_extraidos):
    """
    Converte a lista de clientes para um DataFrame pandas

    Args:
        clientes_extraidos: Lista de dicionários com informações dos clientes

    Returns:
        DataFrame pandas
    """
    df = pd.DataFrame(clientes_extraidos)
    return df

def salvar_clientes(clientes, nome_arquivo):
    """
    Salva os clientes extraídos em um arquivo Excel

    Args:
        clientes: Lista de dicionários com informações dos clientes
        nome_arquivo: Caminho do arquivo onde os dados serão salvos
    """
    try:
        logger.info(f"Salvando {len(clientes)} leads no arquivo: {nome_arquivo}")

        # Criar diretório se não existir
        diretorio = os.path.dirname(nome_arquivo)
        if diretorio and not os.path.exists(diretorio):
            os.makedirs(diretorio)

        # Converter para DataFrame e salvar
        df = df_clientes(clientes)

        # Adicionar timestamp
        hora_extracao = datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        df['Data de Extração'] = hora_extracao

        # Salvar arquivo
        df.to_excel(nome_arquivo, index=False, engine='openpyxl')
        logger.info(f"Arquivo salvo com sucesso: {nome_arquivo}")
        return True

    except Exception as e:
        logger.error(f"Erro ao salvar arquivo {nome_arquivo}: {str(e)}")
        raise e
