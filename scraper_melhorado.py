"""
SCRAPER MELHORADO - Implementação das estratégias do MELHORIAS_SCRAPER.md

Este módulo implementa as melhorias sugeridas no documento MELHORIAS_SCRAPER.md:
1. Extração baseada em place_id para evitar duplicatas
2. Estratégia de tiles geográficos para superar limite de ~120 resultados
3. Extração direta da lista lateral sem clicar em cada elemento
4. Integração opcional com Google Places API para dados detalhados
5. Melhor tratamento de erros e recuperação de falhas

Autor: Sistema de IA baseado nas estratégias documentadas
Data: 2024
"""

import os
import sys
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

# Importar funções do logic_bot melhorado
from logic_bot import (
    buscar_cep,
    buscar_palavra_chave,
    extrair_clientes_com_detalhes_completos,
    extrair_clientes_melhorado_com_detalhes,
    salvar_clientes,
    logger
)

class ScraperMelhorado:
    """
    Classe principal do scraper melhorado que implementa todas as estratégias otimizadas
    """
    
    def __init__(self, api_key=None, headless=False):
        """
        Inicializa o scraper melhorado
        
        Args:
            api_key: Chave da Google Places API (opcional, para dados detalhados)
            headless: Se True, executa o Chrome em modo headless
        """
        self.api_key = api_key
        self.headless = headless
        self.driver = None
        
        # Configurar logging específico
        self.setup_logging()
        
    def setup_logging(self):
        """Configura logging específico para o scraper melhorado"""
        log_filename = f"scraper_melhorado_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        # Criar handler específico para este scraper
        file_handler = logging.FileHandler(log_filename)
        file_handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        # Adicionar handler ao logger principal
        logger.addHandler(file_handler)
        
        logger.info("=== SCRAPER MELHORADO INICIALIZADO ===")
        logger.info(f"API Key fornecida: {'Sim' if self.api_key else 'Não'}")
        logger.info(f"Modo headless: {self.headless}")
    
    def configurar_driver(self):
        """
        Configura e inicializa o WebDriver do Chrome com opções otimizadas
        
        Returns:
            WebDriver configurado ou None se houver erro
        """
        try:
            logger.info("Configurando WebDriver...")
            
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # Opções otimizadas para scraping
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # User agent para parecer mais humano
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Configurações adicionais
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.implicitly_wait(10)
            
            logger.info("WebDriver configurado com sucesso")
            return self.driver
            
        except Exception as e:
            logger.error(f"Erro ao configurar WebDriver: {str(e)}")
            return None
    
    def extrair_leads(self, cep, palavra_chave, quantidade_desejada, 
                     usar_tiles=True, grid_size=5, progress_callback=None):
        """
        Método principal para extrair leads usando as estratégias melhoradas
        
        Args:
            cep: CEP da região para busca
            palavra_chave: Termo a ser buscado (ex: "restaurante", "dentista")
            quantidade_desejada: Número de leads desejados
            usar_tiles: Se True, usa estratégia de tiles geográficos
            grid_size: Tamanho da grade de tiles (ex: 5 = 5x5 = 25 tiles)
            progress_callback: Função de callback para progresso (opcional)
        
        Returns:
            Lista de dicionários com dados dos leads extraídos
        """
        if not self.driver:
            logger.error("Driver não configurado. Chame configurar_driver() primeiro.")
            return []
        
        try:
            logger.info(f"=== INICIANDO EXTRAÇÃO DE LEADS ===")
            logger.info(f"CEP: {cep}")
            logger.info(f"Palavra-chave: {palavra_chave}")
            logger.info(f"Quantidade desejada: {quantidade_desejada}")
            logger.info(f"Estratégia: {'Tiles geográficos' if usar_tiles else 'Rolagem tradicional'}")
            
            # Navegar para o Google Maps
            logger.info("Navegando para o Google Maps...")
            self.driver.get('https://www.google.com/maps/')
            time.sleep(3)
            
            # Buscar CEP
            logger.info(f"Buscando CEP: {cep}")
            if not buscar_cep(self.driver, cep):
                logger.error("Falha ao buscar CEP")
                return []
            
            # Buscar palavra-chave
            logger.info(f"Buscando palavra-chave: {palavra_chave}")
            if not buscar_palavra_chave(self.driver, palavra_chave):
                logger.error("Falha ao buscar palavra-chave")
                return []
            
            # Extrair leads usando estratégias melhoradas
            leads = extrair_clientes_com_detalhes_completos(
                self.driver, 
                quantidade_desejada, 
                progress_callback or self._default_progress_callback,
                usar_tiles=usar_tiles,
                api_key=self.api_key,
                grid_size=grid_size
            )
            
            logger.info(f"=== EXTRAÇÃO CONCLUÍDA: {len(leads)} leads extraídos ===")
            return leads
            
        except Exception as e:
            logger.error(f"Erro durante extração de leads: {str(e)}")
            return []
    
    def _default_progress_callback(self, atual, total, cliente):
        """Callback padrão para progresso"""
        porcentagem = (atual / total) * 100
        logger.info(f"Progresso: {atual}/{total} ({porcentagem:.1f}%) - {cliente.get('nome', 'N/A')}")
    
    def salvar_resultados(self, leads, nome_arquivo=None):
        """
        Salva os resultados em arquivo Excel
        
        Args:
            leads: Lista de leads extraídos
            nome_arquivo: Nome do arquivo (opcional, será gerado automaticamente se não fornecido)
        
        Returns:
            Caminho do arquivo salvo ou None se houver erro
        """
        try:
            if not nome_arquivo:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                nome_arquivo = f"leads_extraidos_{timestamp}.xlsx"
            
            # Garantir extensão .xlsx
            if not nome_arquivo.endswith('.xlsx'):
                nome_arquivo += '.xlsx'
            
            # Salvar usando função do logic_bot
            salvar_clientes(leads, nome_arquivo)
            
            logger.info(f"Resultados salvos em: {nome_arquivo}")
            return nome_arquivo
            
        except Exception as e:
            logger.error(f"Erro ao salvar resultados: {str(e)}")
            return None
    
    def fechar(self):
        """Fecha o WebDriver e limpa recursos"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver fechado com sucesso")
        except Exception as e:
            logger.error(f"Erro ao fechar WebDriver: {str(e)}")
    
    def __enter__(self):
        """Context manager entry"""
        self.configurar_driver()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.fechar()

def exemplo_uso():
    """
    Exemplo de como usar o scraper melhorado
    """
    # Configurações
    CEP = "01310-100"  # Paulista, SP
    PALAVRA_CHAVE = "restaurante"
    QUANTIDADE_DESEJADA = 50
    API_KEY = None  # Substitua pela sua chave da Google Places API se tiver
    
    # Usar context manager para garantir limpeza de recursos
    with ScraperMelhorado(api_key=API_KEY, headless=False) as scraper:
        # Extrair leads
        leads = scraper.extrair_leads(
            cep=CEP,
            palavra_chave=PALAVRA_CHAVE,
            quantidade_desejada=QUANTIDADE_DESEJADA,
            usar_tiles=True,  # Usar estratégia de tiles
            grid_size=5       # Grade 5x5 = 25 tiles
        )
        
        if leads:
            # Salvar resultados
            arquivo = scraper.salvar_resultados(leads)
            print(f"✅ Extração concluída! {len(leads)} leads salvos em: {arquivo}")
        else:
            print("❌ Nenhum lead foi extraído")

if __name__ == "__main__":
    exemplo_uso()
