"""
Teste de Integração da UI com Scraper Melhorado

Este script testa se a integração entre a interface gráfica e o scraper melhorado
está funcionando corretamente.
"""

import sys
import os
from datetime import datetime

def testar_imports():
    """Testa se todos os imports necessários estão funcionando"""
    print("🔍 TESTANDO IMPORTS...")
    
    try:
        # Testar import do scraper melhorado
        from scraper_melhorado import ScraperMelhorado
        print("✅ ScraperMelhorado importado com sucesso")
        scraper_disponivel = True
    except ImportError as e:
        print(f"❌ Erro ao importar ScraperMelhorado: {e}")
        scraper_disponivel = False
    
    try:
        # Testar import das funções melhoradas do logic_bot
        from logic_bot import (
            extrair_clientes_melhorado,
            extrair_clientes_com_detalhes_completos,
            obter_coordenadas_do_mapa,
            gerar_tiles_geograficos
        )
        print("✅ Funções melhoradas do logic_bot importadas com sucesso")
        logic_melhorado = True
    except ImportError as e:
        print(f"❌ Erro ao importar funções melhoradas: {e}")
        logic_melhorado = False
    
    try:
        # Testar import da UI
        from ui import (
            ThreadScraperMelhorado,
            SCRAPER_MELHORADO_DISPONIVEL,
            TelaConfiguracao,
            TelaOperacao
        )
        print("✅ Classes da UI importadas com sucesso")
        print(f"   SCRAPER_MELHORADO_DISPONIVEL = {SCRAPER_MELHORADO_DISPONIVEL}")
        ui_disponivel = True
    except ImportError as e:
        print(f"❌ Erro ao importar classes da UI: {e}")
        ui_disponivel = False
    
    return scraper_disponivel, logic_melhorado, ui_disponivel

def testar_configuracao_scraper():
    """Testa a configuração básica do scraper melhorado"""
    print("\n🔧 TESTANDO CONFIGURAÇÃO DO SCRAPER...")
    
    try:
        from scraper_melhorado import ScraperMelhorado
        
        # Testar criação do scraper sem API key
        scraper = ScraperMelhorado(api_key=None, headless=True)
        print("✅ ScraperMelhorado criado sem API key")
        
        # Testar criação do scraper com API key fictícia
        scraper_com_api = ScraperMelhorado(api_key="test_key", headless=True)
        print("✅ ScraperMelhorado criado com API key")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na configuração do scraper: {e}")
        return False

def testar_thread_scraper():
    """Testa a criação do ThreadScraperMelhorado"""
    print("\n🧵 TESTANDO THREAD SCRAPER MELHORADO...")
    
    try:
        from ui import ThreadScraperMelhorado
        
        # Testar criação da thread
        thread = ThreadScraperMelhorado(
            estado="São Paulo (SP)",
            bairro="Vila Madalena",
            palavra_chave="restaurante",
            quantidade=5,
            usar_tiles=True,
            api_key=None
        )
        print("✅ ThreadScraperMelhorado criado com sucesso")
        print(f"   Estado: {thread.estado}")
        print(f"   Bairro: {thread.bairro}")
        print(f"   Palavra-chave: {thread.palavra_chave}")
        print(f"   Quantidade: {thread.quantidade}")
        print(f"   Usar tiles: {thread.usar_tiles}")
        print(f"   API key: {'Configurada' if thread.api_key else 'Não configurada'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na criação da thread: {e}")
        return False

def testar_funcoes_utilitarias():
    """Testa as funções utilitárias do logic_bot melhorado"""
    print("\n🛠️ TESTANDO FUNÇÕES UTILITÁRIAS...")
    
    try:
        from logic_bot import (
            extrair_place_id_do_href,
            gerar_tiles_geograficos,
            obter_detalhes_place_api
        )
        
        # Testar extração de place_id
        href_teste = "https://www.google.com/maps/place/?q=place_id:ChIJN1t_tDeuEmsRUsoyG83frY4"
        place_id = extrair_place_id_do_href(href_teste)
        print(f"✅ Extração de place_id: {place_id}")
        
        # Testar geração de tiles
        tiles = gerar_tiles_geograficos(-23.550520, -46.633308, desloc=0.01, grid_size=3)
        print(f"✅ Geração de tiles: {len(tiles)} tiles criados")
        
        # Testar função da API (sem fazer chamada real)
        detalhes = obter_detalhes_place_api("test_place_id", None)
        print(f"✅ Função da API testada (retorno: {detalhes})")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro nas funções utilitárias: {e}")
        return False

def testar_configuracao_ui():
    """Testa se a UI pode ser configurada com as novas opções"""
    print("\n🖥️ TESTANDO CONFIGURAÇÃO DA UI...")
    
    try:
        # Simular configuração que seria criada pela TelaConfiguracao
        configuracao_teste = {
            'estado': 'São Paulo (SP)',
            'bairro': 'Vila Madalena',
            'palavra_chave': 'restaurante',
            'quantidade': 10,
            'usar_scraper_melhorado': True,
            'usar_tiles': True,
            'api_key': None,
            'metodo_descricao': 'Melhorado (Tiles Geográficos) - Recomendado'
        }
        
        print("✅ Configuração de teste criada:")
        for chave, valor in configuracao_teste.items():
            print(f"   {chave}: {valor}")
        
        return True, configuracao_teste
        
    except Exception as e:
        print(f"❌ Erro na configuração da UI: {e}")
        return False, None

def gerar_relatorio(resultados):
    """Gera um relatório dos testes"""
    print("\n📋 RELATÓRIO DOS TESTES")
    print("=" * 50)
    
    total_testes = len(resultados)
    testes_ok = sum(1 for r in resultados.values() if r)
    
    print(f"Total de testes: {total_testes}")
    print(f"Testes bem-sucedidos: {testes_ok}")
    print(f"Testes falharam: {total_testes - testes_ok}")
    print(f"Taxa de sucesso: {(testes_ok/total_testes)*100:.1f}%")
    
    print("\nDetalhes:")
    for teste, resultado in resultados.items():
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"  {teste}: {status}")
    
    if testes_ok == total_testes:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("A integração está funcionando corretamente.")
    else:
        print("\n⚠️ ALGUNS TESTES FALHARAM")
        print("Verifique os erros acima e corrija os problemas.")

def main():
    """Executa todos os testes de integração"""
    print("🚀 INICIANDO TESTES DE INTEGRAÇÃO")
    print(f"⏰ Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    resultados = {}
    
    try:
        # Teste 1: Imports
        scraper_ok, logic_ok, ui_ok = testar_imports()
        resultados['Imports Scraper'] = scraper_ok
        resultados['Imports Logic'] = logic_ok
        resultados['Imports UI'] = ui_ok
        
        # Teste 2: Configuração do scraper (só se imports ok)
        if scraper_ok:
            config_ok = testar_configuracao_scraper()
            resultados['Configuração Scraper'] = config_ok
        else:
            resultados['Configuração Scraper'] = False
        
        # Teste 3: Thread scraper (só se UI ok)
        if ui_ok:
            thread_ok = testar_thread_scraper()
            resultados['Thread Scraper'] = thread_ok
        else:
            resultados['Thread Scraper'] = False
        
        # Teste 4: Funções utilitárias (só se logic ok)
        if logic_ok:
            utils_ok = testar_funcoes_utilitarias()
            resultados['Funções Utilitárias'] = utils_ok
        else:
            resultados['Funções Utilitárias'] = False
        
        # Teste 5: Configuração UI
        ui_config_ok, config = testar_configuracao_ui()
        resultados['Configuração UI'] = ui_config_ok
        
        # Gerar relatório final
        gerar_relatorio(resultados)
        
        # Salvar log dos testes
        log_filename = f"teste_integracao_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        with open(log_filename, 'w', encoding='utf-8') as f:
            f.write(f"Teste de Integração - {datetime.now()}\n")
            f.write("=" * 50 + "\n")
            for teste, resultado in resultados.items():
                f.write(f"{teste}: {'PASSOU' if resultado else 'FALHOU'}\n")
        
        print(f"\n📄 Log salvo em: {log_filename}")
        
    except KeyboardInterrupt:
        print("\n⏹️ Testes interrompidos pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro inesperado durante os testes: {e}")

if __name__ == "__main__":
    main()
