"""
Teste do Scraper Melhorado COM Telefone e Site

Este script testa o scraper melhorado que combina:
1. Estratégia de tiles geográficos (superar limite de 120)
2. Extração de telefone e site (clicando em cada elemento)
3. Controle de duplicatas com place_id
4. Melhor tratamento de erros
"""

import time
from datetime import datetime
from scraper_melhorado import ScraperMelhorado

def teste_com_telefone_e_site():
    """
    Testa o scraper melhorado extraindo telefone e site
    """
    print("🧪 TESTE: SCRAPER MELHORADO COM TELEFONE E SITE")
    print("=" * 60)
    
    # Configurações do teste
    CEP = "01310-100"  # Paulista, SP
    PALAVRA_CHAVE = "restaurante"
    QUANTIDADE_TESTE = 10  # Quantidade pequena para teste
    
    print(f"📍 CEP: {CEP}")
    print(f"🔍 Palavra-chave: {PALAVRA_CHAVE}")
    print(f"📊 Quantidade: {QUANTIDADE_TESTE}")
    print(f"⚙️ Método: Tiles geográficos + Clique para detalhes")
    print()
    
    def callback_progresso(atual, total, cliente):
        """Callback para mostrar progresso detalhado"""
        porcentagem = (atual / total) * 100
        print(f"📈 Progresso: {atual}/{total} ({porcentagem:.1f}%)")
        print(f"   👤 Nome: {cliente.get('nome', 'N/A')}")
        print(f"   📞 Telefone: {cliente.get('telefone', 'N/A')}")
        print(f"   🌐 Site: {cliente.get('site', 'N/A')}")
        print(f"   📍 Endereço: {cliente.get('endereco', 'N/A')}")
        print("-" * 40)
    
    try:
        print("🚀 Iniciando teste...")
        inicio = time.time()
        
        # Usar scraper melhorado SEM API (mas COM telefone e site)
        with ScraperMelhorado(api_key=None, headless=False) as scraper:
            leads = scraper.extrair_leads(
                cep=CEP,
                palavra_chave=PALAVRA_CHAVE,
                quantidade_desejada=QUANTIDADE_TESTE,
                usar_tiles=True,  # Usar tiles para superar limite
                grid_size=3,      # Grade 3x3 = 9 tiles (suficiente para teste)
                progress_callback=callback_progresso
            )
            
            tempo_total = time.time() - inicio
            
            print("\n🎉 TESTE CONCLUÍDO!")
            print("=" * 60)
            print(f"⏱️ Tempo total: {tempo_total:.1f} segundos")
            print(f"📊 Leads extraídos: {len(leads)}")
            
            if leads:
                # Analisar qualidade dos dados
                com_telefone = sum(1 for lead in leads if lead.get('telefone') != 'Telefone não disponível')
                com_site = sum(1 for lead in leads if lead.get('site') != 'Site não disponível')
                com_endereco = sum(1 for lead in leads if lead.get('endereco') != 'Endereço não disponível')
                
                print(f"📞 Com telefone: {com_telefone}/{len(leads)} ({(com_telefone/len(leads)*100):.1f}%)")
                print(f"🌐 Com site: {com_site}/{len(leads)} ({(com_site/len(leads)*100):.1f}%)")
                print(f"📍 Com endereço: {com_endereco}/{len(leads)} ({(com_endereco/len(leads)*100):.1f}%)")
                
                print("\n📋 EXEMPLOS EXTRAÍDOS:")
                print("-" * 60)
                for i, lead in enumerate(leads[:5]):  # Mostrar primeiros 5
                    print(f"{i+1}. {lead.get('nome', 'N/A')}")
                    print(f"   📞 {lead.get('telefone', 'N/A')}")
                    print(f"   🌐 {lead.get('site', 'N/A')}")
                    print(f"   📍 {lead.get('endereco', 'N/A')}")
                    print()
                
                # Salvar resultados
                arquivo = scraper.salvar_resultados(leads)
                print(f"💾 Resultados salvos em: {arquivo}")
                
                # Verificar duplicatas
                place_ids = [lead.get('place_id') for lead in leads if lead.get('place_id')]
                place_ids_unicos = set(place_ids)
                duplicatas = len(place_ids) - len(place_ids_unicos)
                
                print(f"🔍 Controle de duplicatas: {duplicatas} duplicatas encontradas")
                
                return True, leads
            else:
                print("❌ Nenhum lead foi extraído")
                return False, []
                
    except Exception as e:
        print(f"❌ Erro durante o teste: {str(e)}")
        return False, []

def comparar_com_metodo_tradicional():
    """
    Compara o método melhorado com o tradicional
    """
    print("\n🔄 COMPARAÇÃO: MELHORADO vs TRADICIONAL")
    print("=" * 60)
    
    # Teste 1: Método melhorado
    print("1️⃣ Testando método MELHORADO...")
    sucesso_melhorado, leads_melhorado = teste_com_telefone_e_site()
    
    if sucesso_melhorado:
        print(f"✅ Método melhorado: {len(leads_melhorado)} leads extraídos")
    else:
        print("❌ Método melhorado falhou")
    
    # Análise final
    print("\n📊 ANÁLISE FINAL:")
    print("-" * 30)
    
    if sucesso_melhorado:
        print("🎯 VANTAGENS DO MÉTODO MELHORADO:")
        print("   ✅ Usa tiles geográficos (supera limite de ~120)")
        print("   ✅ Extrai telefone e site (clicando em cada elemento)")
        print("   ✅ Controla duplicatas com place_id")
        print("   ✅ Melhor tratamento de erros")
        print("   ✅ Logs detalhados para diagnóstico")
        
        # Calcular estatísticas
        com_telefone = sum(1 for lead in leads_melhorado if lead.get('telefone') != 'Telefone não disponível')
        taxa_telefone = (com_telefone / len(leads_melhorado)) * 100 if leads_melhorado else 0
        
        print(f"\n📈 ESTATÍSTICAS:")
        print(f"   📞 Taxa de sucesso telefone: {taxa_telefone:.1f}%")
        print(f"   📊 Total de leads: {len(leads_melhorado)}")
        
    else:
        print("❌ Teste falhou - verificar logs para diagnóstico")

def main():
    """
    Executa o teste principal
    """
    print("🚀 INICIANDO TESTE DO SCRAPER MELHORADO")
    print(f"⏰ Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # Executar teste principal
        comparar_com_metodo_tradicional()
        
        print("\n🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("=" * 60)
        print("📝 Verifique os arquivos gerados:")
        print("   • leads_extraidos_*.xlsx (resultados)")
        print("   • scraper_melhorado_*.log (logs detalhados)")
        
    except KeyboardInterrupt:
        print("\n⏹️ Teste interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {str(e)}")

if __name__ == "__main__":
    main()
