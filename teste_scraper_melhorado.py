"""
Teste do Scraper Melhorado - Demonstração das melhorias implementadas

Este script testa as funcionalidades melhoradas do scraper e compara
com o método tradicional para demonstrar as vantagens.
"""

import time
import sys
import os
from datetime import datetime
from scraper_melhorado import ScraperMelhorado
from logic_bot import logger

def teste_comparativo():
    """
    Executa teste comparativo entre método tradicional e melhorado
    """
    print("🧪 TESTE COMPARATIVO: MÉTODO TRADICIONAL vs MELHORADO")
    print("=" * 60)
    
    # Configurações do teste
    CEP = "01310-100"  # Paulista, SP
    PALAVRA_CHAVE = "loja"
    QUANTIDADE_TESTE = 20  # Quantidade pequena para teste rápido
    
    resultados = {}
    
    # Teste 1: Método tradicional (sem tiles)
    print("\n1️⃣ TESTANDO MÉTODO TRADICIONAL (sem tiles)")
    print("-" * 40)
    
    try:
        with ScraperMelhorado(headless=False) as scraper:
            inicio = time.time()
            
            leads_tradicional = scraper.extrair_leads(
                cep=CEP,
                palavra_chave=PALAVRA_CHAVE,
                quantidade_desejada=QUANTIDADE_TESTE,
                usar_tiles=False  # Método tradicional
            )
            
            tempo_tradicional = time.time() - inicio
            
            resultados['tradicional'] = {
                'leads': len(leads_tradicional),
                'tempo': tempo_tradicional,
                'dados': leads_tradicional
            }
            
            print(f"✅ Método tradicional: {len(leads_tradicional)} leads em {tempo_tradicional:.1f}s")
            
    except Exception as e:
        print(f"❌ Erro no método tradicional: {str(e)}")
        resultados['tradicional'] = {'leads': 0, 'tempo': 0, 'erro': str(e)}
    
    # Pequena pausa entre testes
    time.sleep(5)
    
    # Teste 2: Método melhorado (com tiles)
    print("\n2️⃣ TESTANDO MÉTODO MELHORADO (com tiles)")
    print("-" * 40)
    
    try:
        with ScraperMelhorado(headless=False) as scraper:
            inicio = time.time()
            
            leads_melhorado = scraper.extrair_leads(
                cep=CEP,
                palavra_chave=PALAVRA_CHAVE,
                quantidade_desejada=QUANTIDADE_TESTE,
                usar_tiles=True,  # Método melhorado
                grid_size=3       # Grade pequena para teste
            )
            
            tempo_melhorado = time.time() - inicio
            
            resultados['melhorado'] = {
                'leads': len(leads_melhorado),
                'tempo': tempo_melhorado,
                'dados': leads_melhorado
            }
            
            print(f"✅ Método melhorado: {len(leads_melhorado)} leads em {tempo_melhorado:.1f}s")
            
    except Exception as e:
        print(f"❌ Erro no método melhorado: {str(e)}")
        resultados['melhorado'] = {'leads': 0, 'tempo': 0, 'erro': str(e)}
    
    # Análise dos resultados
    print("\n📊 ANÁLISE COMPARATIVA")
    print("=" * 60)
    
    if 'tradicional' in resultados and 'melhorado' in resultados:
        trad = resultados['tradicional']
        melh = resultados['melhorado']
        
        print(f"Leads Extraídos:")
        print(f"  • Tradicional: {trad['leads']}")
        print(f"  • Melhorado:   {melh['leads']}")
        
        if melh['leads'] > trad['leads']:
            diferenca = melh['leads'] - trad['leads']
            print(f"  🎯 Método melhorado encontrou {diferenca} leads a mais!")
        
        print(f"\nTempo de Execução:")
        print(f"  • Tradicional: {trad['tempo']:.1f}s")
        print(f"  • Melhorado:   {melh['tempo']:.1f}s")
        
        # Verificar duplicatas
        if 'dados' in trad and 'dados' in melh:
            place_ids_trad = set()
            place_ids_melh = set()
            
            for lead in trad['dados']:
                if 'place_id' in lead:
                    place_ids_trad.add(lead['place_id'])
            
            for lead in melh['dados']:
                if 'place_id' in lead:
                    place_ids_melh.add(lead['place_id'])
            
            duplicatas_trad = len(trad['dados']) - len(place_ids_trad)
            duplicatas_melh = len(melh['dados']) - len(place_ids_melh)
            
            print(f"\nControle de Duplicatas:")
            print(f"  • Tradicional: {duplicatas_trad} duplicatas")
            print(f"  • Melhorado:   {duplicatas_melh} duplicatas")
            
            if duplicatas_melh < duplicatas_trad:
                print(f"  🎯 Método melhorado evitou {duplicatas_trad - duplicatas_melh} duplicatas!")
    
    return resultados

def teste_tiles_diferentes():
    """
    Testa diferentes tamanhos de grade de tiles
    """
    print("\n🗺️ TESTE DE DIFERENTES TAMANHOS DE TILES")
    print("=" * 60)
    
    CEP = "01310-100"
    PALAVRA_CHAVE = "restaurante"
    QUANTIDADE_TESTE = 15
    
    tamanhos_grid = [3, 5]  # Testar grades 3x3 e 5x5
    
    for grid_size in tamanhos_grid:
        print(f"\n🔍 Testando grade {grid_size}x{grid_size} ({grid_size**2} tiles)")
        print("-" * 30)
        
        try:
            with ScraperMelhorado(headless=False) as scraper:
                inicio = time.time()
                
                leads = scraper.extrair_leads(
                    cep=CEP,
                    palavra_chave=PALAVRA_CHAVE,
                    quantidade_desejada=QUANTIDADE_TESTE,
                    usar_tiles=True,
                    grid_size=grid_size
                )
                
                tempo = time.time() - inicio
                
                print(f"✅ Grade {grid_size}x{grid_size}: {len(leads)} leads em {tempo:.1f}s")
                
                # Mostrar alguns exemplos
                if leads:
                    print("📋 Exemplos extraídos:")
                    for i, lead in enumerate(leads[:3]):
                        print(f"  {i+1}. {lead.get('nome', 'N/A')} - {lead.get('endereco', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Erro com grade {grid_size}x{grid_size}: {str(e)}")
        
        time.sleep(3)  # Pausa entre testes

def teste_com_api_key():
    """
    Demonstra como usar com Google Places API (se disponível)
    """
    print("\n🔑 TESTE COM GOOGLE PLACES API")
    print("=" * 60)
    
    # Verificar se há API key configurada
    api_key = os.getenv('GOOGLE_PLACES_API_KEY')
    
    if not api_key:
        print("ℹ️ Google Places API Key não configurada")
        print("   Para testar com API:")
        print("   1. Obtenha uma chave em: https://console.cloud.google.com/")
        print("   2. Configure: export GOOGLE_PLACES_API_KEY='sua_chave'")
        print("   3. Execute novamente este teste")
        return
    
    print(f"✅ API Key encontrada: {api_key[:10]}...")
    
    CEP = "01310-100"
    PALAVRA_CHAVE = "farmacia"
    QUANTIDADE_TESTE = 5  # Poucos para não gastar cota
    
    try:
        with ScraperMelhorado(api_key=api_key, headless=False) as scraper:
            print("🔍 Extraindo com dados enriquecidos da Places API...")
            
            leads = scraper.extrair_leads(
                cep=CEP,
                palavra_chave=PALAVRA_CHAVE,
                quantidade_desejada=QUANTIDADE_TESTE,
                usar_tiles=True,
                grid_size=3
            )
            
            if leads:
                print(f"✅ {len(leads)} leads extraídos com dados enriquecidos")
                print("\n📋 Exemplo de dados enriquecidos:")
                
                for i, lead in enumerate(leads[:2]):
                    print(f"\n  Lead {i+1}:")
                    print(f"    Nome: {lead.get('nome', 'N/A')}")
                    print(f"    Telefone: {lead.get('telefone', 'N/A')}")
                    print(f"    Endereço: {lead.get('endereco', 'N/A')}")
                    print(f"    Site: {lead.get('site', 'N/A')}")
                    print(f"    Avaliação: {lead.get('avaliacao', 'N/A')}")
            else:
                print("❌ Nenhum lead extraído")
                
    except Exception as e:
        print(f"❌ Erro no teste com API: {str(e)}")

def main():
    """
    Executa todos os testes do scraper melhorado
    """
    print("🚀 INICIANDO TESTES DO SCRAPER MELHORADO")
    print(f"⏰ Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # Teste 1: Comparativo tradicional vs melhorado
        resultados_comparativo = teste_comparativo()
        
        # Teste 2: Diferentes tamanhos de tiles
        teste_tiles_diferentes()
        
        # Teste 3: Com API key (se disponível)
        teste_com_api_key()
        
        print("\n🎉 TODOS OS TESTES CONCLUÍDOS!")
        print("=" * 60)
        print("📝 Verifique os logs gerados para detalhes técnicos")
        print("📊 Os resultados demonstram as melhorias implementadas")
        
    except KeyboardInterrupt:
        print("\n⏹️ Testes interrompidos pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante os testes: {str(e)}")
        logger.error(f"Erro nos testes: {str(e)}")

if __name__ == "__main__":
    main()
